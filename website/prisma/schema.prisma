// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// User management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  role      UserRole @default(EDITOR)
  name      String?
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  pages        Page[]
  media        Media[]
  programs     Program[]
  chinaPrograms ChinaProgram[]
  internationalPrograms InternationalProgram[]
  sessions     Session[]
  testimonials Testimonial[]
  blogs        Blog[]
  videos       Video[]

  @@map("users")
}

model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

enum UserRole {
  ADMIN
  EDITOR
  VIEWER
}

// Content management - Post models removed as they were unused
// Blog model provides the actual blog functionality

// Page management for static content
model Page {
  id          String     @id @default(cuid())
  title       String
  slug        String     @unique
  content     String
  status      PageStatus @default(DRAFT)
  language    String     @default("zh")
  template    String?
  seoTitle    String?
  seoDescription String?
  publishedAt DateTime?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  authorId    String

  author       User             @relation(fields: [authorId], references: [id])
  translations PageTranslation[]

  @@map("pages")
}

model PageTranslation {
  id          String @id @default(cuid())
  pageId      String
  language    String
  title       String
  content     String
  seoTitle    String?
  seoDescription String?

  page Page @relation(fields: [pageId], references: [id], onDelete: Cascade)

  @@unique([pageId, language])
  @@map("page_translations")
}

enum PageStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

// Media management
model Media {
  id          String    @id @default(cuid())
  filename    String
  originalName String
  mimeType    String
  size        Int
  url         String
  alt         String?
  caption     String?
  order       Int       @default(0)
  uploadedBy  String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  uploader     User          @relation(fields: [uploadedBy], references: [id])
  testimonials Testimonial[]
  blogs        Blog[]
  blogCarousels BlogCarousel[]
  videos       Video[]       // 视频封面图关联

  @@map("media")
}

// Video management for blog featured videos
model Video {
  id          String    @id @default(cuid())
  slug        String    @unique
  isPublished Boolean   @default(false)
  order       Int       @default(0)

  // 视频类型 (双语)
  categoryZh  String
  categoryEn  String

  // 视频标题 (双语)
  titleZh     String
  titleEn     String

  // 视频简介 (双语)
  descriptionZh String
  descriptionEn String

  // 视频封面图
  thumbnailId String?
  thumbnail   Media?    @relation(fields: [thumbnailId], references: [id])

  // 视频文件或链接
  videoFile   String?   // 上传的视频文件路径
  videoUrl    String?   // 外部视频链接

  // 元数据
  uploadedBy  String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  uploader    User      @relation(fields: [uploadedBy], references: [id])

  @@map("videos")
}

// Website settings and configuration
model Setting {
  id        String   @id @default(cuid())
  key       String
  value     String
  type      SettingType @default(TEXT)
  language  String   @default("zh")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  translations SettingTranslation[]

  @@unique([key, language])
  @@map("settings")
}

model SettingTranslation {
  id        String @id @default(cuid())
  settingId String
  language  String
  value     String

  setting Setting @relation(fields: [settingId], references: [id], onDelete: Cascade)

  @@unique([settingId, language])
  @@map("setting_translations")
}

enum SettingType {
  TEXT
  TEXTAREA
  HTML
  JSON
  BOOLEAN
  NUMBER
  URL
  EMAIL
}

// Program management for study abroad programs
model Program {
  id          String        @id @default(cuid())
  title       String
  slug        String        @unique
  description String
  content     String
  status      ProgramStatus @default(DRAFT)
  language    String        @default("zh")
  country     String
  city        String
  duration    String
  price       Float?
  currency    String        @default("CNY")
  maxStudents Int?
  minAge      Int?
  maxAge      Int?
  startDate   DateTime?
  endDate     DateTime?
  deadline    DateTime?
  featuredImage String?
  gallery     String? // JSON string array
  highlights  String? // JSON string array
  academics   String? // JSON string array
  itinerary   String? // JSON string array
  requirements String? // JSON string array
  materials   String? // JSON string array
  seoTitle    String?
  seoDescription String?
  publishedAt DateTime?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  authorId    String

  author       User                @relation(fields: [authorId], references: [id])
  translations ProgramTranslation[]
  applications Application[]

  @@map("programs")
}

model ProgramTranslation {
  id          String   @id @default(cuid())
  programId   String
  language    String
  title       String
  description String
  content     String
  highlights  String? // JSON string array
  academics   String? // JSON string array
  itinerary   String? // JSON string array
  requirements String? // JSON string array
  materials   String? // JSON string array
  seoTitle    String?
  seoDescription String?

  program Program @relation(fields: [programId], references: [id], onDelete: Cascade)

  @@unique([programId, language])
  @@map("program_translations")
}

enum ProgramStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
  FULL
}

// Application management
model Application {
  id          String            @id @default(cuid())
  programId   String
  studentName String
  studentEmail String
  studentPhone String?
  studentAge  Int?
  parentName  String?
  parentEmail String?
  parentPhone String?
  status      ApplicationStatus @default(PENDING)
  notes       String?
  submittedAt DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  program Program @relation(fields: [programId], references: [id])

  @@map("applications")
}

enum ApplicationStatus {
  PENDING
  APPROVED
  REJECTED
  WAITLIST
}

// Newsletter subscriptions
model Newsletter {
  id          String   @id @default(cuid())
  email       String   @unique
  name        String?
  language    String   @default("zh")
  isActive    Boolean  @default(true)
  subscribedAt DateTime @default(now())
  unsubscribedAt DateTime?

  @@map("newsletters")
}

// Testimonials management
model Testimonial {
  id          String              @id @default(cuid())
  content     String
  author      String
  role        String
  program     String
  status      TestimonialStatus   @default(PUBLISHED)
  language    String              @default("zh")
  imageId     String?
  order       Int                 @default(0)
  publishedAt DateTime?
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt
  authorId    String

  // Relations
  authorUser   User                    @relation(fields: [authorId], references: [id])
  image        Media?                  @relation(fields: [imageId], references: [id])
  translations TestimonialTranslation[]

  @@map("testimonials")
}

model TestimonialTranslation {
  id            String @id @default(cuid())
  testimonialId String
  language      String
  content       String
  author        String
  role          String
  program       String

  testimonial Testimonial @relation(fields: [testimonialId], references: [id], onDelete: Cascade)

  @@unique([testimonialId, language])
  @@map("testimonial_translations")
}

enum TestimonialStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

// Blog management
model Blog {
  id          String      @id @default(cuid())
  title       String
  slug        String      @unique
  content     String
  author      String      // 作者字段
  program     String      // 项目字段
  grade       String      // 级别字段
  status      BlogStatus  @default(PUBLISHED)
  language    String      @default("zh")
  imageId     String?
  order       Int         @default(0)
  publishedAt DateTime?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  authorId    String

  // Relations
  authorUser     User              @relation(fields: [authorId], references: [id])
  image          Media?            @relation(fields: [imageId], references: [id])
  translations   BlogTranslation[]
  carouselImages BlogCarousel[]

  @@map("blogs")
}

model BlogTranslation {
  id       String @id @default(cuid())
  blogId   String
  language String
  title    String
  slug     String
  content  String
  author   String  // 作者字段
  program  String  // 项目字段
  grade    String  // 级别字段

  blog Blog @relation(fields: [blogId], references: [id], onDelete: Cascade)

  @@unique([blogId, language])
  @@map("blog_translations")
}

// Blog carousel images
model BlogCarousel {
  id      String @id @default(cuid())
  blogId  String
  mediaId String
  order   Int    @default(0)

  blog  Blog  @relation(fields: [blogId], references: [id], onDelete: Cascade)
  media Media @relation(fields: [mediaId], references: [id], onDelete: Cascade)

  @@unique([blogId, mediaId])
  @@map("blog_carousels")
}

enum BlogStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

// China Program management for study in China programs
model ChinaProgram {
  id          String             @id @default(cuid())
  title       String
  slug        String             @unique
  description String
  status      ChinaProgramStatus @default(DRAFT)
  language    String             @default("zh")
  country     String?
  cityId      String?
  duration    String
  deadline    DateTime?
  featuredImage String?
  gallery     String? // JSON string array
  highlights  String? // JSON string array
  academics   String? // JSON string array
  itinerary   String? // JSON string array
  requirements String? // JSON string array
  type        String? // JSON string array for program types
  gradeLevel  String? // JSON string array for grade levels
  sessions    String? // JSON string array for session dates
  publishedAt DateTime?
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  authorId    String

  author       User                      @relation(fields: [authorId], references: [id])
  city         City?                     @relation(fields: [cityId], references: [id])
  translations ChinaProgramTranslation[]
  applications ChinaApplication[]

  @@map("china_programs")
}

model ChinaProgramTranslation {
  id          String   @id @default(cuid())
  programId   String
  language    String
  title       String
  description String
  duration    String? // 项目时长的翻译
  highlights  String? // JSON string array
  academics   String? // JSON string array
  itinerary   String? // JSON string array
  requirements String? // JSON string array
  sessions    String? // JSON string array for session dates
  materials   String? // JSON string array

  program ChinaProgram @relation(fields: [programId], references: [id], onDelete: Cascade)

  @@unique([programId, language])
  @@map("china_program_translations")
}

enum ChinaProgramStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
  FULL
}

// China Application management
model ChinaApplication {
  id          String                   @id @default(cuid())
  programId   String
  studentName String
  studentEmail String
  studentPhone String?
  studentAge  Int?
  parentName  String?
  parentEmail String?
  parentPhone String?
  status      ChinaApplicationStatus   @default(PENDING)
  notes       String?
  submittedAt DateTime                 @default(now())
  updatedAt   DateTime                 @updatedAt

  program ChinaProgram @relation(fields: [programId], references: [id])

  @@map("china_applications")
}

enum ChinaApplicationStatus {
  PENDING
  APPROVED
  REJECTED
  WAITLIST
}

// Shared Fields Management
model ProgramType {
  id          String   @id @default(cuid())
  name        String
  nameEn      String?
  description String?
  isActive    Boolean  @default(true)
  order       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("program_types")
}

model GradeLevel {
  id          String   @id @default(cuid())
  name        String
  nameEn      String?
  description String?
  isActive    Boolean  @default(true)
  order       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("grade_levels")
}

model Country {
  id          String   @id @default(cuid())
  name        String
  nameEn      String?
  code        String?  // ISO country code
  isActive    Boolean  @default(true)
  order       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  cities      City[]

  @@map("countries")
}

model City {
  id          String   @id @default(cuid())
  name        String
  nameEn      String?
  countryId   String
  isActive    Boolean  @default(true)
  order       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  country       Country        @relation(fields: [countryId], references: [id], onDelete: Cascade)
  chinaPrograms ChinaProgram[]
  internationalPrograms InternationalProgram[]

  @@unique([name, countryId])
  @@map("cities")
}

// International Program management for study abroad programs
model InternationalProgram {
  id          String                      @id @default(cuid())
  title       String
  slug        String                      @unique
  description String
  status      InternationalProgramStatus  @default(DRAFT)
  language    String                      @default("zh")
  country     String?
  cityId      String?
  duration    String
  deadline    DateTime?
  featuredImage String?
  gallery     String? // JSON string array
  highlights  String? // JSON string array
  academics   String? // JSON string array
  itinerary   String? // JSON string array
  requirements String? // JSON string array
  type        String? // JSON string array for program types
  gradeLevel  String? // JSON string array for grade levels
  sessions    String? // JSON string array for session dates
  publishedAt DateTime?
  createdAt   DateTime                    @default(now())
  updatedAt   DateTime                    @updatedAt
  authorId    String

  author       User                              @relation(fields: [authorId], references: [id])
  city         City?                             @relation(fields: [cityId], references: [id])
  translations InternationalProgramTranslation[]
  applications InternationalApplication[]

  @@map("international_programs")
}

model InternationalProgramTranslation {
  id          String   @id @default(cuid())
  programId   String
  language    String
  title       String
  description String
  duration    String? // 项目时长的翻译
  highlights  String? // JSON string array
  academics   String? // JSON string array
  itinerary   String? // JSON string array
  requirements String? // JSON string array
  sessions    String? // JSON string array for session dates
  materials   String? // JSON string array

  program InternationalProgram @relation(fields: [programId], references: [id], onDelete: Cascade)

  @@unique([programId, language])
  @@map("international_program_translations")
}

enum InternationalProgramStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
  FULL
}

// International Application management
model InternationalApplication {
  id          String                           @id @default(cuid())
  programId   String
  studentName String
  studentEmail String
  studentPhone String?
  studentAge  Int?
  parentName  String?
  parentEmail String?
  parentPhone String?
  status      InternationalApplicationStatus   @default(PENDING)
  notes       String?
  submittedAt DateTime                         @default(now())
  updatedAt   DateTime                         @updatedAt

  program InternationalProgram @relation(fields: [programId], references: [id])

  @@map("international_applications")
}

enum InternationalApplicationStatus {
  PENDING
  APPROVED
  REJECTED
  WAITLIST
}
